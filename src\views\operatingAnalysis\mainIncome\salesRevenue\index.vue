<template>
  <div class="salesRevenue">
    <div class="content-up">
      <div class="main-indicators">
        <chartBox :title="'主要指标总览'">
          <template v-slot:box-right>
            <DatePicker
              v-model="newDateValue"
              dateType="day"
              @change="logChange('新日期选择', $event)"
            />
          </template>
          <CarouselBtn :buttons="buttons" />
          <div class="card-box">
            <ItemCard
              v-for="item in cardData"
              :key="item.title"
              :info="item"
              class="item-card"
            />
          </div>
        </chartBox>
      </div>
      <div class="trend-box">
        <chartBox :title="'分终端日销量趋势'"></chartBox>
      </div>
    </div>
    <div class="content-down">
      <div class="statistics-box">
        <chartBox :title="'分油气田统计'"></chartBox>
      </div>
      <div class="completion-rate">
        <chartBox :title="'同比/环比增减动因'"></chartBox>
      </div>
    </div>
  </div>
</template>
<script>
import CarouselBtn from "../../components/CarouselBtn.vue";
import ItemCard from "../../components/ItemCard.vue";
export default {
  name: "salesRevenue",
  components: {
    CarouselBtn,
    ItemCard,
  },
  data() {
    return {
      newDateValue: "",
      buttons: [
        "作业公司",
        "YC13-1",
        "YC13-10",
        "LS25-1",
        "LS17-2",
        "文昌16-2",
      ],
      cardData: [
        { title: "油气合计收入", value: "10000" },
        { title: "天然气收入", value: "10000" },
        { title: "凝析油收入", value: "10000" },
        { title: "原油收入", value: "10000" },
      ],
    };
  },
};
</script>
<style lang="scss" scoped>
.salesRevenue {
  .content-up {
    display: flex;
    justify-content: space-between;
    .main-indicators {
      flex: 1;
      margin-right: 10px;
    }
    .trend-box {
      flex: 1;
    }

    .card-box {
      display: flex;
      justify-content: space-between;
      margin: 8px 20px; // 减少边距，避免内容溢出
      flex: 1; // 占据剩余空间
      min-height: 0; // 允许flex收缩
      gap: 12px; // 使用gap替代margin-right

      .item-card {
        flex: 1;
        min-width: 0; // 允许收缩
        height: 100%; // 确保卡片填满容器高度
      }
    }
  }

  .content-down {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
    .statistics-box {
      flex: 1;
    }

    .completion-rate {
      flex: 1;
      margin: 0 10px;
    }
  }
}
</style>
